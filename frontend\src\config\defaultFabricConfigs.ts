// =============================================================================
// FABRIC CONFIGURATION CONSTANTS
// =============================================================================

// Default image dimensions when actual dimensions are not available
export const DEFAULT_IMAGE_WIDTH = 512;
export const DEFAULT_IMAGE_HEIGHT = 512;

// Canvas sizing constants
export const MIN_CANVAS_WIDTH = 300;
export const MIN_CANVAS_HEIGHT = 200;

// Resize threshold - minimum pixel difference to trigger a resize
export const RESIZE_THRESHOLD = 5;

// Text styling constants
export const DEFAULT_TEXT_SIZE = 16;
export const DEFAULT_TEXT_SCALE = 1;

// Object ID generation constants
export const OBJECT_ID_PREFIX = "obj_";
export const OBJECT_ID_RANDOM_LENGTH = 11;

// Object names for identification
export const BACKGROUND_IMAGE_NAME = "background-image";
export const CROP_RECT_NAME = "crop-rect";
export const MEASUREMENT_LINE_NAME = "measurement-line";
export const MEASUREMENT_TEXT_NAME = "measurement-text";

// Object names that should be excluded from certain operations
export const EXCLUDED_OBJECT_NAMES = ["background-image", "crop-rect"];

// Default viewport transform (identity matrix)
export const IDENTITY_VIEWPORT_TRANSFORM: [number, number, number, number, number, number] = [
  1, 0, 0, 1, 0, 0,
];

// Object property defaults
export const DEFAULT_OBJECT_SELECTABLE = false;
export const DEFAULT_OBJECT_EVENTED = false;
export const DEFAULT_STROKE_UNIFORM = true;

// Tool styling constants
export const DEFAULT_TOOL_COLOR = "red";
export const DEFAULT_STROKE_WIDTH = 2;
export const CROP_STROKE_COLOR = "#ff0000";
export const CROP_FILL_COLOR = "rgba(255, 255, 255, .1)";
export const MEASUREMENT_COLOR = "#ff6b35";

// Rotation angles
export const ROTATION_ANGLES = {
  NONE: 0,
  QUARTER: 90,
  HALF: 180,
  THREE_QUARTER: 270,
} as const;

// Common rotation angles for positioning calculations
export const VERTICAL_ROTATION_ANGLES = [0, 180];
export const HORIZONTAL_ROTATION_ANGLES = [90, 270];

// =============================================================================
// DEFAULT FABRIC CONFIGURATIONS
// =============================================================================

export const DEFAULT_FABRIC_CONFIGS = {
  brightness: 1,
  contrast: 1,
  grayscale: false,
  invert: false,
  sharpness: 1,
  gammaR: 1,
  gammaG: 1,
  gammaB: 1,
  annotations: {
    version: "6.6.6",
    objects: [],
    background: "transparent",
    canvasWidth: DEFAULT_IMAGE_WIDTH,
    canvasHeight: DEFAULT_IMAGE_HEIGHT,
  },
  cropData: {
    isCropped: false,
    normalizedCropRect: null,
    canvasDimensions: undefined,
  },
  transformState: {
    rotations: 0,
    flipHorizontal: false,
    flipVertical: false,
  },
};
