import { useRef, useState } from "react";
import { Canvas } from "fabric";
import { UndoAction, FabricMeasurementLine, UndoTrackingState } from "@/shared/types";
import { createHandleUndo } from "@/lib/fabric/actions";

export const useUndoTracking = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialObjectCount: React.MutableRefObject<number>
): UndoTrackingState => {
  const [undoStack, setUndoStack] = useState<UndoAction[]>([]);
  const isUndoingRef = useRef<boolean>(false);

  // Create undo handler
  const handleUndo = createHandleUndo(
    fabricCanvas,
    undoStack,
    setUndoStack,
    initialObjectCount,
    isUndoingRef
  );

  // Add action to undo stack
  const addUndoAction = (action: UndoAction) => {
    if (isUndoingRef.current) return;
    setUndoStack((prev) => [...prev, action]);
  };

  // Disable undo tracking (used during operations that shouldn't be tracked)
  const disableUndoTracking = () => {
    isUndoingRef.current = true;
  };

  // Enable undo tracking
  const enableUndoTracking = () => {
    isUndoingRef.current = false;
  };

  return {
    undoStack,
    canUndo: undoStack.length > 0,
    isUndoingRef,
    handleUndo,
    disableUndoTracking,
    enableUndoTracking,
    addUndoAction,
  };
};
