import { Canvas, Textbox } from "fabric";
import { ToolMode, FabricMeasurementLine } from "@/shared/types";
import { constrainToCanvas, transformPointer } from "./toolConfigs";
import { createInitialShape, updateShapeSize } from "./shapeCreators";
import { updateMeasurementText } from "@/lib/fabric/operations/measurements";

export const handleTextClick = (
  pointer: { x: number; y: number },
  canvas: Canvas,
  onShapeCreated?: () => void
) => {
  const objects = canvas.getObjects();
  const clickedObject = objects.find((obj) => {
    if (obj.type === "textbox") {
      const objBounds = obj.getBoundingRect();
      return (
        pointer.x >= objBounds.left &&
        pointer.x <= objBounds.left + objBounds.width &&
        pointer.y >= objBounds.top &&
        pointer.y <= objBounds.top + objBounds.height
      );
    }
    return false;
  });

  if (clickedObject) {
    canvas.setActiveObject(clickedObject);
    (clickedObject as Textbox).enterEditing();
    return true;
  }

  const transformedPointer = transformPointer(pointer, canvas);
  const shape = createInitialShape("text", transformedPointer, canvas);
  if (!shape) return false;

  canvas.add(shape);
  canvas.setActiveObject(shape);
  const textbox = shape as Textbox;
  textbox.enterEditing();
  textbox.selectAll();
  if (onShapeCreated) onShapeCreated();
  return true;
};

export const handleMouseDown = (
  pointer: { x: number; y: number },
  activeMode: ToolMode,
  canvas: Canvas,
  isShowingOriginal: boolean,
  onShapeCreated?: () => void
) => {
  if (!activeMode || activeMode === "select" || isShowingOriginal) {
    return null;
  }

  const constrainedPointer = constrainToCanvas(pointer, canvas);

  if (activeMode === "text") {
    handleTextClick(constrainedPointer, canvas, onShapeCreated);
    return null;
  }

  const transformedPointer = transformPointer(constrainedPointer, canvas);
  const shape = createInitialShape(activeMode, transformedPointer, canvas);
  if (!shape) return null;

  canvas.add(shape);
  return { shape, startPoint: transformedPointer };
};

export const handleMouseMove = (
  pointer: { x: number; y: number },
  activeMode: ToolMode,
  canvas: Canvas,
  currentShape: any,
  startPoint: { x: number; y: number },
  isShowingOriginal: boolean,
  disableUndoTracking?: () => void,
  enableUndoTracking?: () => void
) => {
  if (!currentShape || !startPoint || isShowingOriginal) return;

  const constrainedPointer = constrainToCanvas(pointer, canvas);
  const transformedPointer = transformPointer(constrainedPointer, canvas);

  updateShapeSize(currentShape, startPoint, transformedPointer, activeMode, canvas);
  
  if (activeMode === "measure") {
    const line = currentShape as FabricMeasurementLine;
    if (line.measurementText) {
      disableUndoTracking?.();
      canvas.remove(line.measurementText);
      enableUndoTracking?.();
      line.measurementText = undefined;
    }
    disableUndoTracking?.();
    updateMeasurementText(canvas, line);
    enableUndoTracking?.();
  }
  canvas.renderAll();
};

export const handleMouseUp = (
  activeMode: ToolMode,
  currentShape: any,
  canvas: Canvas,
  isShowingOriginal: boolean,
  onCrop?: () => void,
  onShapeCreated?: () => void,
  disableUndoTracking?: () => void,
  enableUndoTracking?: () => void
) => {
  if (!currentShape || isShowingOriginal) return;

  if (activeMode === "crop" && onCrop) onCrop();

  if (activeMode === "measure") {
    const line = currentShape as FabricMeasurementLine;
    if (!line.measurementText) {
      disableUndoTracking?.();
      updateMeasurementText(canvas, line);
      enableUndoTracking?.();
    }
  }

  if (onShapeCreated && activeMode !== "crop") onShapeCreated();
};
