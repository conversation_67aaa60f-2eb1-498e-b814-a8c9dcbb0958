import { Canvas } from "fabric";
import { ToolMode } from "@/shared/types";
import {
  DEFAULT_STROKE_WIDTH,
  DEFAULT_TEXT_SIZE,
  DEFAULT_TOOL_COLOR,
  CROP_STROKE_COLOR,
  CROP_FILL_COLOR,
  MEASUREMENT_COLOR,
  DEFAULT_STROKE_UNIFORM,
} from "@/config/defaultFabricConfigs";

export const getToolConfig = (mode: ToolMode, canvas?: Canvas) => {
  const vpt = canvas?.viewportTransform;
  const viewportScale = vpt ? vpt[0] : 1;
  const adjustedStrokeWidth = DEFAULT_STROKE_WIDTH / viewportScale;

  switch (mode) {
    case "freehand":
      return {
        color: DEFAULT_TOOL_COLOR,
        width: 3 / viewportScale,
        strokeUniform: DEFAULT_STROKE_UNIFORM,
      };
    case "text":
      return {
        fontSize: DEFAULT_TEXT_SIZE,
        fill: DEFAULT_TOOL_COLOR,
        width: 200,
        selectable: false,
        evented: false,
        strokeUniform: DEFAULT_STROKE_UNIFORM,
      };
    case "rect":
      return {
        width: 1,
        height: 1,
        fill: "transparent",
        stroke: DEFAULT_TOOL_COLOR,
        strokeWidth: adjustedStrokeWidth,
        strokeUniform: DEFAULT_STROKE_UNIFORM,
        selectable: false,
        evented: false,
      };
    case "circle":
      return {
        radius: 1,
        fill: "transparent",
        stroke: DEFAULT_TOOL_COLOR,
        strokeWidth: adjustedStrokeWidth,
        selectable: false,
        evented: false,
        strokeUniform: DEFAULT_STROKE_UNIFORM,
      };
    case "line":
      return {
        stroke: DEFAULT_TOOL_COLOR,
        strokeWidth: adjustedStrokeWidth,
        selectable: false,
        evented: false,
        strokeUniform: DEFAULT_STROKE_UNIFORM,
      };
    case "crop":
      return {
        width: 1,
        height: 1,
        fill: CROP_FILL_COLOR,
        stroke: CROP_STROKE_COLOR,
        strokeWidth: 0.5,
        strokeDashArray: [5, 5],
        selectable: false,
        evented: false,
        strokeUniform: DEFAULT_STROKE_UNIFORM,
        name: "crop-rect",
      };
    case "measure":
      return {
        strokeWidth: adjustedStrokeWidth,
        fill: MEASUREMENT_COLOR,
        stroke: MEASUREMENT_COLOR,
        originX: "center",
        originY: "center",
        selectable: false,
        evented: false,
        hasControls: false,
        hasBorders: false,
        lockScalingFlip: true,
        name: "measurement-line",
        strokeUniform: DEFAULT_STROKE_UNIFORM,
      };
    default:
      return {};
  }
};

export const constrainToCanvas = (pointer: { x: number; y: number }, canvas: Canvas) => {
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();

  return {
    x: Math.max(0, Math.min(pointer.x, canvasWidth - 1)),
    y: Math.max(0, Math.min(pointer.y, canvasHeight - 1)),
  };
};

export const transformPointer = (
  pointer: { x: number; y: number },
  canvas: Canvas
): { x: number; y: number } => {
  const vpt = canvas.viewportTransform;
  const needsTransform = vpt && (vpt[0] !== 1 || vpt[3] !== 1 || vpt[4] !== 0 || vpt[5] !== 0);

  if (needsTransform) {
    return {
      x: (pointer.x - vpt[4]) / vpt[0],
      y: (pointer.y - vpt[5]) / vpt[3],
    };
  }

  return pointer;
};
