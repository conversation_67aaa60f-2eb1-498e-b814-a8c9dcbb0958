import { useEffect, useState } from "react";
import { PencilBrush, Rect, Circle, Line, Textbox } from "fabric";
import { ToolMode, FabricMeasurementLine, UseFabricToolsProps } from "@/shared/types";
import {
  setToolMode,
  getToolConfig,
  startDrawingShape,
  updateDrawingShape,
  finishDrawingShape,
} from "@/lib/fabric/tools";
import { cleanupOrphanedMeasurementTexts } from "@/lib/fabric/operations/measurements";

export const useFabricTools = ({
  fabricCanvas,
  isShowingOriginal = false,
  hasPerformedCrop = false,
  onShapeCreated,
  onCrop,
  disableUndoTracking,
  enableUndoTracking,
}: UseFabricToolsProps) => {
  const [activeMode, setActiveMode] = useState<ToolMode | null>(null);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [currentShape, setCurrentShape] = useState<
    Rect | Circle | Line | FabricMeasurementLine | Textbox | null
  >(null);

  const changeToolMode = (mode: ToolMode) => {
    if (!fabricCanvas?.current) return;

    if (activeMode === "measure" && mode !== "measure") {
      cleanupOrphanedMeasurementTexts(fabricCanvas.current);
    }

    setActiveMode(mode);
    setToolMode(mode, fabricCanvas.current);
  };

  useEffect(() => {
    if (!fabricCanvas?.current) return;

    const canvas = fabricCanvas.current;
    const config = getToolConfig("freehand") as any;
    canvas.freeDrawingBrush = new PencilBrush(canvas);
    canvas.freeDrawingBrush.color = config.color;
    canvas.freeDrawingBrush.width = config.width;

    if (activeMode) {
      setToolMode(activeMode, canvas);
    }

    const mouseDownHandler = (e: { pointer: { x: number; y: number } }) => {
      if (!activeMode || !e.pointer) return;

      const result = startDrawingShape(
        e.pointer,
        activeMode,
        canvas,
        isShowingOriginal,
        hasPerformedCrop,
        onShapeCreated
      );

      if (result) {
        setStartPoint(result.startPoint);
        setCurrentShape(result.shape);
      }
    };

    const mouseMoveHandler = (e: { pointer: { x: number; y: number } }) => {
      if (!currentShape || !startPoint || !e.pointer || !activeMode) return;

      updateDrawingShape(
        e.pointer,
        activeMode,
        canvas,
        currentShape,
        startPoint,
        isShowingOriginal,
        disableUndoTracking,
        enableUndoTracking
      );
    };

    const mouseUpHandler = () => {
      if (!currentShape || !activeMode) {
        setStartPoint(null);
        setCurrentShape(null);
        return;
      }

      finishDrawingShape(
        activeMode,
        currentShape,
        canvas,
        isShowingOriginal,
        onCrop,
        onShapeCreated,
        disableUndoTracking,
        enableUndoTracking
      );

      setStartPoint(null);
      setCurrentShape(null);
    };

    const disposeMouseDown = canvas.on("mouse:down", mouseDownHandler);
    const disposeMouseMove = canvas.on("mouse:move", mouseMoveHandler);
    const disposeMouseUp = canvas.on("mouse:up", mouseUpHandler);

    return () => {
      disposeMouseDown();
      disposeMouseMove();
      disposeMouseUp();
    };
  }, [
    fabricCanvas,
    activeMode,
    startPoint,
    currentShape,
    onShapeCreated,
    isShowingOriginal,
    hasPerformedCrop,
    onCrop,
    disableUndoTracking,
    enableUndoTracking,
  ]);

  return {
    activeMode,
    changeToolMode,
  };
};
