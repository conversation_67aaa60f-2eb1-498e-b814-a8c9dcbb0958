import { useEffect, useState } from "react";
import { PencilBrush, Rect, Circle, Line, Textbox } from "fabric";
import { ToolMode, FabricMeasurementLine, UseFabricToolsProps } from "@/shared/types";
import {
  setToolMode,
  getToolConfig,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
} from "@/lib/fabric/tools";
import { cleanupOrphanedMeasurementTexts } from "@/lib/fabric/operations/measurements";

export const useFabricTools = ({
  fabricCanvas,
  isShowingOriginal = false,
  onShapeCreated,
  onCrop,
  disableUndoTracking,
  enableUndoTracking,
}: UseFabricToolsProps) => {
  const [activeMode, setActiveMode] = useState<ToolMode | null>(null);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [currentShape, setCurrentShape] = useState<
    Rect | Circle | Line | FabricMeasurementLine | Textbox | null
  >(null);

  const changeToolMode = (mode: ToolMode) => {
    if (!fabricCanvas?.current) return;

    if (activeMode === "measure" && mode !== "measure") {
      cleanupOrphanedMeasurementTexts(fabricCanvas.current);
    }

    setActiveMode(mode);
    setToolMode(mode, fabricCanvas.current);
  };

  useEffect(() => {
    if (!fabricCanvas?.current) return;

    const canvas = fabricCanvas.current;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const config = getToolConfig("freehand", canvas) as any;
    canvas.freeDrawingBrush = new PencilBrush(canvas);
    canvas.freeDrawingBrush.color = config.color;
    canvas.freeDrawingBrush.width = config.width;

    if (activeMode) {
      setToolMode(activeMode, canvas);
    }

    const mouseDownHandler = (e: { pointer: { x: number; y: number } }) => {
      if (!activeMode || !e.pointer) return;

      const result = handleMouseDown(
        e.pointer,
        activeMode,
        canvas,
        isShowingOriginal,
        onShapeCreated
      );

      if (result) {
        setStartPoint(result.startPoint);
        setCurrentShape(result.shape);
      }
    };

    const mouseMoveHandler = (e: { pointer: { x: number; y: number } }) => {
      if (!currentShape || !startPoint || !e.pointer || !activeMode) return;

      handleMouseMove(
        e.pointer,
        activeMode,
        canvas,
        currentShape,
        startPoint,
        isShowingOriginal,
        disableUndoTracking,
        enableUndoTracking
      );
    };

    const mouseUpHandler = () => {
      if (!currentShape || !activeMode) {
        setStartPoint(null);
        setCurrentShape(null);
        return;
      }

      handleMouseUp(
        activeMode,
        currentShape,
        canvas,
        isShowingOriginal,
        onCrop,
        onShapeCreated,
        disableUndoTracking,
        enableUndoTracking
      );

      setStartPoint(null);
      setCurrentShape(null);
    };

    const disposeMouseDown = canvas.on("mouse:down", mouseDownHandler);
    const disposeMouseMove = canvas.on("mouse:move", mouseMoveHandler);
    const disposeMouseUp = canvas.on("mouse:up", mouseUpHandler);

    return () => {
      disposeMouseDown();
      disposeMouseMove();
      disposeMouseUp();
    };
  }, [
    fabricCanvas,
    activeMode,
    startPoint,
    currentShape,
    onShapeCreated,
    isShowingOriginal,
    onCrop,
    disableUndoTracking,
    enableUndoTracking,
  ]);

  return {
    activeMode,
    changeToolMode,
  };
};
