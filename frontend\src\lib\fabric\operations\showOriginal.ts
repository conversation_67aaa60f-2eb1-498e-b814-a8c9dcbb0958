import { Canvas } from "fabric";
import {
  applyCanvasFilters,
  loadAnnotations,
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "@/lib/fabric/operations";

// Adjust existing annotations to maintain consistent coordinate system after crop
const adjustAnnotationsForCrop = (
  canvas: Canvas,
  scaleX: number,
  scaleY: number,
  cropLeft: number,
  cropTop: number
): void => {
  canvas.forEachObject((obj) => {
    // Skip background image and crop rectangles
    const objName = (obj as any).name;
    if (objName === "background-image" || objName === "crop-rect") {
      return;
    }

    // Get current object properties
    const currentLeft = obj.left || 0;
    const currentTop = obj.top || 0;
    const currentScaleX = obj.scaleX || 1;
    const currentScaleY = obj.scaleY || 1;

    // Calculate new position accounting for crop offset and scale
    const newLeft = (currentLeft - cropLeft) * scaleX;
    const newTop = (currentTop - cropTop) * scaleY;

    // For stroke width consistency, we need to adjust it inversely to the scale
    if ("strokeWidth" in obj && obj.strokeWidth) {
      const newStrokeWidth = obj.strokeWidth / Math.min(scaleX, scaleY);
      obj.set({ strokeWidth: newStrokeWidth });
    }

    // For text objects, maintain readable size
    if (obj.type === "textbox" || obj.type === "text") {
      // Keep text at readable size by adjusting scale inversely
      const textScaleX = currentScaleX / scaleX;
      const textScaleY = currentScaleY / scaleY;
      obj.set({
        scaleX: textScaleX,
        scaleY: textScaleY,
      });
    }

    // Update object position
    obj.set({
      left: newLeft,
      top: newTop,
    });

    obj.setCoords();
  });
};
import { loadCanvasImage } from "@/lib/fabric/rendering";
import { DEFAULT_FABRIC_CONFIGS } from "@/config/defaultFabricConfigs";
import { CropData, TransformState } from "@/shared/types";

export const createShowOriginalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  isShowingOriginal: boolean,
  setIsShowingOriginal: (value: boolean) => void,
  isUndoing: React.MutableRefObject<boolean>,
  cachedState: React.MutableRefObject<{
    annotations: any;
    filters: Record<string, any>;
    cropData: CropData;
    transformState: TransformState;
  } | null>,
  filters: Record<string, any>,
  canvasCropData: CropData,
  transformState: TransformState,
  originalImageUrl: React.MutableRefObject<string>,
  setCropData: (data: CropData) => void,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    isUndoing.current = true;

    if (!isShowingOriginal) {
      cachedState.current = {
        annotations: canvas.toObject(["name", "id"]),
        filters: filters,
        cropData: canvasCropData,
        transformState,
      };

      Array.from({ length: (4 - transformState.rotations) % 4 }, () => applyCanvasRotation(canvas));
      if (transformState.flipHorizontal) applyCanvasFlipHorizontal(canvas);
      if (transformState.flipVertical) applyCanvasFlipVertical(canvas);
      setTransformState({ rotations: 0, flipHorizontal: false, flipVertical: false });

      canvas.forEachObject((obj) => {
        if ((obj as any).name !== "background-image") canvas.remove(obj);
      });

      canvas.clipPath = undefined;
      canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

      if (containerRef?.current) {
        const containerBounds = containerRef.current.getBoundingClientRect();
        canvas.setDimensions({ width: containerBounds.width, height: containerBounds.height });
      }

      applyCanvasFilters(canvas, DEFAULT_FABRIC_CONFIGS);
      const containerRect = containerRef?.current?.getBoundingClientRect();
      await loadCanvasImage(canvas, originalImageUrl.current, {
        containerRect: containerRect || undefined,
      });
      setIsShowingOriginal(true);
    } else {
      if (cachedState.current) {
        const containerRect = containerRef?.current?.getBoundingClientRect();
        await loadCanvasImage(canvas, originalImageUrl.current, {
          containerRect: containerRect || undefined,
        });

        applyCanvasFilters(canvas, cachedState.current.filters);

        for (let i = 0; i < cachedState.current.transformState.rotations; i++) {
          applyCanvasRotation(canvas);
        }
        if (cachedState.current.transformState.flipHorizontal) applyCanvasFlipHorizontal(canvas);
        if (cachedState.current.transformState.flipVertical) applyCanvasFlipVertical(canvas);

        await loadAnnotations(canvas, cachedState.current.annotations);

        if (
          cachedState.current.cropData?.isCropped &&
          cachedState.current.cropData.normalizedCropRect
        ) {
          const { normalizedCropRect } = cachedState.current.cropData;

          if (canvas.backgroundImage) {
            const bgImage = canvas.backgroundImage;
            const scaledImageWidth = (bgImage.width || 512) * (bgImage.scaleX || 1);
            const scaledImageHeight = (bgImage.height || 512) * (bgImage.scaleY || 1);

            const left = normalizedCropRect.left * scaledImageWidth;
            const top = normalizedCropRect.top * scaledImageHeight;
            const width = normalizedCropRect.width * scaledImageWidth;
            const height = normalizedCropRect.height * scaledImageHeight;

            if (containerRef?.current) {
              const containerRect = containerRef.current.getBoundingClientRect();
              const aspectRatio = width / height;
              let targetCanvasWidth = containerRect.width;
              let targetCanvasHeight = targetCanvasWidth / aspectRatio;

              if (targetCanvasHeight > containerRect.height) {
                targetCanvasHeight = containerRect.height;
                targetCanvasWidth = targetCanvasHeight * aspectRatio;
              }

              canvas.setDimensions({ width: targetCanvasWidth, height: targetCanvasHeight });

              const scaleX = targetCanvasWidth / width;
              const scaleY = targetCanvasHeight / height;
              const scale = Math.max(scaleX, scaleY);
              const vpt: [number, number, number, number, number, number] = [
                scale,
                0,
                0,
                scale,
                -left * scale,
                -top * scale,
              ];
              canvas.setViewportTransform(vpt);

              // Adjust annotations for crop transform
              adjustAnnotationsForCrop(canvas, scale, scale, left, top);
            }
          }
        }

        setCropData(cachedState.current.cropData);
        setTransformState(cachedState.current.transformState);

        setHasPerformedCrop(cachedState.current.cropData?.isCropped || false);
      }
      setIsShowingOriginal(false);
    }
    isUndoing.current = false;
  };
};
