import { useEffect, useRef, useState } from "react";
import { Types, Enums, imageLoader, RenderingEngine } from "@cornerstonejs/core";
import FabricToolbar from "@/components/toolbars/FabricToolbar";
import { initializeCornerstone, setup2dViewport, loadDicomStack } from "@/lib/dicom/core";
import { ImageViewerProps } from "@/shared/types";
import { useFabricViewer } from "@/hooks";
import { useResponsiveCanvas } from "@/hooks";

const StackViewer: React.FC<ImageViewerProps> = ({ data }) => {
  const cornerstoneRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const viewportRef = useRef<Types.IStackViewport | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasFabricCanvas, setHasFabricCanvas] = useState(false);

  const {
    canvas: fabricCanvas,
    setupCanvas,
    brightness,
    contrast,
    grayscale,
    invert,
    sharpness,
    gammaR,
    gammaG,
    gammaB,
    handleBrightnessChange,
    handleContrastChange,
    handleGrayscaleChange,
    handleInvertChange,
    handleSharpnessChange,
    handleGammaRChange,
    handleGammaGChange,
    handleGammaBChange,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    handleUndo,
    handleCrop,
    handleSave,
    handleShowOriginal,
    applySavedTransforms,
    disableUndoTracking,
    enableUndoTracking,
    canUndo,
    isShowingOriginal,
    hasPerformedCrop,
    cropData,
    setCropData,
  } = useFabricViewer({
    data,
    containerRef,
  });

  const { resizeCanvas } = useResponsiveCanvas({
    fabricCanvas,
    containerRef,
    cropData,
    setCropData,
  });

  const captureAndTransferToFabric = async () => {
    if (!cornerstoneRef.current || !canvasRef.current) return;
    const cornerstoneCanvas = cornerstoneRef.current.querySelector("canvas");
    if (!cornerstoneCanvas) {
      return;
    }
    const ImageDataUrl = cornerstoneCanvas.toDataURL("image/png");
    await setupCanvas(canvasRef.current, ImageDataUrl);
    setHasFabricCanvas(true);
    resizeCanvas();
    if (!data.viewer.fabricConfigs.cropData?.isCropped) {
      applySavedTransforms();
    }
  };
  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);
    return () => {
      renderingEngineRef.current?.destroy();
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !cornerstoneRef.current) return;
    const element = cornerstoneRef.current;
    async function setupViewer() {
      const renderingEngineId = `CSOIRenderingEngineStack`;
      const currentViewportId = `CSOIViewportStack`;
      // For reading the File Meta Data to get the image dimensions
      const image = await imageLoader.loadAndCacheImage(data.viewer.imageUrl);
      const { width: columns, height: rows } = image;
      element.style.width = `${columns}px`;
      element.style.height = `${rows}px`;

      const renderingEngine = new RenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;
      const viewport = setup2dViewport(renderingEngine, element, currentViewportId);
      viewportRef.current = viewport;
      await loadDicomStack(viewport, data.viewer.imageUrl);
      const handleImageRendered = () => {
        captureAndTransferToFabric();

        // Always call resizeCanvas for both cropped and non-cropped images
        // The useResponsiveCanvas hook will handle the appropriate logic for each case
        resizeCanvas();

        element.removeEventListener(Enums.Events.IMAGE_RENDERED, handleImageRendered);
      };
      element.addEventListener(Enums.Events.IMAGE_RENDERED, handleImageRendered);
    }
    setupViewer();
  }, [isInitialized, data.viewer.imageUrl]);

  return (
    <div className="stack-viewer" id="stack-viewer-container">
      <div
        ref={containerRef}
        className={`viewer-container ${hasPerformedCrop || cropData?.isCropped ? "cropped" : ""}`}
      >
        {!hasFabricCanvas && (
          <div
            ref={cornerstoneRef}
            className="cornerstone-element"
            style={{ position: "absolute", visibility: "hidden" }}
          />
        )}
        <canvas ref={canvasRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gammaR={gammaR}
        gammaG={gammaG}
        gammaB={gammaB}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaRChange={handleGammaRChange}
        onGammaGChange={handleGammaGChange}
        onGammaBChange={handleGammaBChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onUndo={handleUndo}
        canUndo={canUndo}
        onSave={handleSave}
        onShowOriginal={handleShowOriginal}
        onCrop={handleCrop}
        disableGrayscale={true}
        disableGamma={true}
        disableUndoTracking={disableUndoTracking}
        enableUndoTracking={enableUndoTracking}
        isShowingOriginal={isShowingOriginal}
        hasPerformedCrop={hasPerformedCrop}
      />
    </div>
  );
};

export default StackViewer;
