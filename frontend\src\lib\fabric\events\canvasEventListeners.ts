/* eslint-disable @typescript-eslint/no-explicit-any */
import { Canvas } from "fabric";
import { updateMeasurementOnModify, isMeasurementLine } from "../operations/measurements";
import {
  FabricMeasurementLine,
  FabricObjectState,
  UndoAction,
  UndoTrackingState,
} from "@/shared/types";

// Create handler for tracking object additions to undo stack
export const createUndoTrackingAddHandler = (undoTracking: UndoTrackingState, canvas: Canvas) => {
  return (e?: any) => {
    if (!undoTracking.isUndoingRef.current) {
      const addedObject = e?.path || e?.target;
      if (addedObject) {
        const objName = (addedObject as any).name;
        if (objName === "measurement-line") {
          undoTracking.addUndoAction({
            type: "add-measurement",
            lineId: addedObject?.id,
          } as UndoAction);
        } else if (!objName || (!objName.includes("background") && objName !== "crop-rect")) {
          undoTracking.addUndoAction({
            type: "add",
            objectCount: canvas.getObjects().length,
          } as UndoAction);
        }
      }
    }
  };
};

// Create handler for tracking object removals to undo stack
export const createUndoTrackingRemoveHandler = (undoTracking: UndoTrackingState) => {
  return () => {
    if (!undoTracking.isUndoingRef.current) {
      undoTracking.addUndoAction({
        type: "remove",
        objectCount: 0, // Will be set by the actual canvas object count
      });
    }
  };
};

// Create handler for tracking object modifications to undo stack
export const createUndoTrackingModifyHandler = (
  undoTracking: UndoTrackingState,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>
) => {
  return (e: { target: unknown }) => {
    if (
      undoTracking.isUndoingRef.current ||
      !e.target ||
      (e.target as any)?.name === "measurement-text"
    )
      return;

    const obj = e.target as any;
    const objId = obj.id as string;
    const previousState = objectStates.current.get(objId);
    if (previousState) {
      undoTracking.addUndoAction({ type: "modify", objectId: objId, previousState });
      objectStates.current.delete(objId);
    }
  };
};

// Create handler for capturing object state before modification starts
export const createObjectStateTrackingHandler = (
  undoTracking: UndoTrackingState,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>
) => {
  return (e: { target: unknown }) => {
    if (
      undoTracking.isUndoingRef.current ||
      !e.target ||
      isMeasurementLine(e.target) ||
      (e.target as any)?.name === "measurement-text"
    )
      return;

    const obj = e.target as any;
    let objId = obj.id as string;
    if (!objId) {
      objId = `obj_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      obj.id = objId;
    }
    if (!objectStates.current.has(objId)) {
      objectStates.current.set(objId, {
        left: obj.left || 0,
        top: obj.top || 0,
        scaleX: obj.scaleX || 1,
        scaleY: obj.scaleY || 1,
        angle: obj.angle || 0,
      });
    }
  };
};

// Create handler for measurement lines with modification tracking
export const createMeasurementModificationHandler = (
  canvas: Canvas,
  trackModifyHandler: (e: { target: unknown }) => void
) => {
  return (e: { target: unknown }) => {
    if (isMeasurementLine(e.target))
      updateMeasurementOnModify(canvas, e.target as FabricMeasurementLine);
    trackModifyHandler(e);
  };
};

// Create handler for measurement line movement updates
export const createMeasurementMovementHandler = (canvas: Canvas) => {
  return (e: { target: unknown }) => {
    if (isMeasurementLine(e.target))
      updateMeasurementOnModify(canvas, e.target as FabricMeasurementLine);
  };
};

// Setup all canvas event listeners
export const setupCanvasEventListeners = (
  canvas: Canvas,
  undoTracking: UndoTrackingState,
  objectStates: React.MutableRefObject<Map<string, FabricObjectState>>
): (() => void)[] => {
  // Create all handlers
  const trackAddHandler = createUndoTrackingAddHandler(undoTracking, canvas);
  const trackRemoveHandler = createUndoTrackingRemoveHandler(undoTracking);
  const trackModifyHandler = createUndoTrackingModifyHandler(undoTracking, objectStates);
  const startMeasurementHandler = createObjectStateTrackingHandler(undoTracking, objectStates);
  const modifyMeasurementHandler = createMeasurementModificationHandler(canvas, trackModifyHandler);
  const moveMeasurementHandler = createMeasurementMovementHandler(canvas);

  // Attach event listeners and return disposers
  return [
    canvas.on("path:created", trackAddHandler),
    canvas.on("object:added", (e) => {
      if (e.target && e.target.type !== "path") {
        trackAddHandler(e);
      }
    }),
    canvas.on("object:removed", trackRemoveHandler),
    canvas.on("object:moving", (e) => {
      startMeasurementHandler(e);
      moveMeasurementHandler(e);
    }),
    canvas.on("object:scaling", (e) => {
      startMeasurementHandler(e);
      moveMeasurementHandler(e);
    }),
    canvas.on("object:rotating", (e) => {
      startMeasurementHandler(e);
      moveMeasurementHandler(e);
    }),
    canvas.on("object:modified", modifyMeasurementHandler),
  ];
};
