import { Canvas } from "fabric";
import { BACKGROUND_IMAGE_NAME } from "@/config/defaultFabricConfigs";

export const setToolMode = (mode: string, canvas: Canvas) => {
  canvas.selection = false;
  canvas.discardActiveObject();
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== BACKGROUND_IMAGE_NAME) {
      obj.selectable = false;
      obj.evented = false;
    }
  });
  canvas.defaultCursor = "crosshair";
  canvas.isDrawingMode = false;
  canvas.selection = false;

  if (mode === "freehand") {
    canvas.isDrawingMode = true;
  } else if (mode === "select") {
    canvas.defaultCursor = "default";
    canvas.isDrawingMode = false;
    canvas.selection = true;

    canvas.forEachObject((obj) => {
      const objName = (obj as unknown as Record<string, unknown>)?.name;
      if (objName !== BACKGROUND_IMAGE_NAME) {
        obj.selectable = true;
        obj.evented = true;
        obj.hasControls = true;
        obj.hasBorders = true;
        obj.setCoords();
      }
    });
  }
  canvas.renderAll();
};
