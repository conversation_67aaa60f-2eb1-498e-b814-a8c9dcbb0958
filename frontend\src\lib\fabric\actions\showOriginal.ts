/* eslint-disable @typescript-eslint/no-explicit-any */
import { Canvas } from "fabric";
import {
  applyCanvasFilters,
  loadAnnotations,
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "@/lib/fabric/operations";
import { loadCanvasImage } from "@/lib/fabric/rendering";
import { DEFAULT_FABRIC_CONFIGS } from "@/config/defaultFabricConfigs";
import { CropData, TransformState } from "@/shared/types";

export const createHandleShowOriginal = (
  fabricCanvas: React.RefObject<Canvas | null>,
  isShowingOriginal: boolean,
  setIsShowingOriginal: (value: boolean) => void,
  isUndoing: React.MutableRefObject<boolean>,
  cachedState: React.MutableRefObject<{
    annotations: any;
    filters: Record<string, any>;
    cropData: CropData;
    transformState: TransformState;
  } | null>,
  filters: Record<string, any>,
  canvasCropData: CropData,
  transformState: TransformState,
  originalImageUrl: React.MutableRefObject<string>,
  setCropData: (data: CropData) => void,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    isUndoing.current = true;

    if (!isShowingOriginal) {
      cachedState.current = {
        annotations: canvas.toObject(["name", "id"]),
        filters: filters,
        cropData: canvasCropData,
        transformState,
      };

      Array.from({ length: (4 - transformState.rotations) % 4 }, () => applyCanvasRotation(canvas));
      if (transformState.flipHorizontal) applyCanvasFlipHorizontal(canvas);
      if (transformState.flipVertical) applyCanvasFlipVertical(canvas);
      setTransformState({ rotations: 0, flipHorizontal: false, flipVertical: false });

      canvas.forEachObject((obj) => {
        if ((obj as any).name !== "background-image") canvas.remove(obj);
      });

      canvas.clipPath = undefined;
      canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

      if (containerRef?.current) {
        const containerBounds = containerRef.current.getBoundingClientRect();
        canvas.setDimensions({ width: containerBounds.width, height: containerBounds.height });
      }

      applyCanvasFilters(canvas, DEFAULT_FABRIC_CONFIGS);
      const containerRect = containerRef?.current?.getBoundingClientRect();
      await loadCanvasImage(canvas, originalImageUrl.current, {
        containerRect: containerRect || undefined,
      });
      setIsShowingOriginal(true);
    } else {
      if (cachedState.current) {
        const containerRect = containerRef?.current?.getBoundingClientRect();
        await loadCanvasImage(canvas, originalImageUrl.current, {
          containerRect: containerRect || undefined,
        });

        applyCanvasFilters(canvas, cachedState.current.filters);

        for (let i = 0; i < cachedState.current.transformState.rotations; i++) {
          applyCanvasRotation(canvas);
        }
        if (cachedState.current.transformState.flipHorizontal) applyCanvasFlipHorizontal(canvas);
        if (cachedState.current.transformState.flipVertical) applyCanvasFlipVertical(canvas);

        await loadAnnotations(canvas, cachedState.current.annotations);

        if (
          cachedState.current.cropData?.isCropped &&
          cachedState.current.cropData.normalizedCropRect
        ) {
          const { normalizedCropRect } = cachedState.current.cropData;

          if (canvas.backgroundImage) {
            const bgImage = canvas.backgroundImage;
            const scaledImageWidth = (bgImage.width || 512) * (bgImage.scaleX || 1);
            const scaledImageHeight = (bgImage.height || 512) * (bgImage.scaleY || 1);

            const left = normalizedCropRect.left * scaledImageWidth;
            const top = normalizedCropRect.top * scaledImageHeight;
            const width = normalizedCropRect.width * scaledImageWidth;
            const height = normalizedCropRect.height * scaledImageHeight;

            if (containerRef?.current) {
              const containerRect = containerRef.current.getBoundingClientRect();
              const aspectRatio = width / height;
              let targetCanvasWidth = containerRect.width;
              let targetCanvasHeight = targetCanvasWidth / aspectRatio;

              if (targetCanvasHeight > containerRect.height) {
                targetCanvasHeight = containerRect.height;
                targetCanvasWidth = targetCanvasHeight * aspectRatio;
              }

              canvas.setDimensions({ width: targetCanvasWidth, height: targetCanvasHeight });

              const scaleX = targetCanvasWidth / width;
              const scaleY = targetCanvasHeight / height;
              const scale = Math.max(scaleX, scaleY);
              const vpt: [number, number, number, number, number, number] = [
                scale,
                0,
                0,
                scale,
                -left * scale,
                -top * scale,
              ];
              canvas.setViewportTransform(vpt);
            }
          }
        }

        setCropData(cachedState.current.cropData);
        setTransformState(cachedState.current.transformState);

        setHasPerformedCrop(cachedState.current.cropData?.isCropped || false);
      }
      setIsShowingOriginal(false);
    }
    isUndoing.current = false;
  };
};
