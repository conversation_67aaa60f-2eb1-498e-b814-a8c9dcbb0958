/* eslint-disable @typescript-eslint/no-explicit-any */
import { Canvas } from "fabric";
import { saveFabricConfig } from "@/shared/api";
import { CropData, FabricConfig, TransformState } from "@/shared/types";

export const createSaveHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  dataId: string,
  filters: Record<string, any>,
  canvasCropData: CropData,
  transformState: TransformState
) => {
  return async () => {
    if (fabricCanvas?.current) {
      const annotations = fabricCanvas.current.toJSON();
      annotations.canvasWidth = fabricCanvas.current.getWidth();
      annotations.canvasHeight = fabricCanvas.current.getHeight();

      const fabricConfig: FabricConfig = {
        brightness: filters.brightness,
        contrast: filters.contrast,
        grayscale: filters.grayscale,
        invert: filters.invert,
        sharpness: filters.sharpness,
        gammaR: filters.gammaR,
        gammaG: filters.gammaG,
        gammaB: filters.gammaB,
        annotations,
        cropData: canvasCropData,
        transformState,
      };

      await saveFabricConfig(dataId, fabricConfig);
    }
  };
};
